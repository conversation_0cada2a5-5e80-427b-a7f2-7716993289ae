<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Models\Lead;
use App\Models\Deal;
use App\Models\Customer;
use App\Models\Pipeline;
use App\Models\Stage;
use App\Models\LeadStage;
use App\Models\Source;
use App\Models\Label;
use App\Models\LeadTask;
use App\Models\DealTask;
use App\Models\LeadCall;
use App\Models\DealCall;
use App\Models\LeadEmail;
use App\Models\DealEmail;
use App\Models\LeadFile;
use App\Models\DealFile;
use App\Models\LeadDiscussion;
use App\Models\DealDiscussion;
use App\Models\ActivityLog;
use App\Models\LeadActivityLog;
use App\Models\User;
use App\Models\ProductService;
use App\Models\CustomField;
use App\Models\CrmCustomField;
use App\Models\CustomFieldValue;
use App\Models\ContactGroup;
use App\Services\CrmWebhookDispatcher;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;

class CrmApiController extends Controller
{
    /**
     * Validate SSO Token middleware
     */
    private function validateSsoToken(Request $request)
    {
        $token = $request->bearerToken() ?? $request->get('token');
        
        if (!$token) {
            return response()->json(['error' => 'SSO token required'], 401);
        }

        try {
            $decoded = JWT::decode($token, new Key(env('SSO_SECRET', 'default-secret'), 'HS256'));
            
            $user = User::find($decoded->user_id);
            if (!$user) {
                return response()->json(['error' => 'Invalid user'], 401);
            }

            // Set the authenticated user for the request
            Auth::setUser($user);
            
            return true;
        } catch (\Exception $e) {
            return response()->json(['error' => 'Invalid or expired SSO token'], 401);
        }
    }

    /**
     * Check if token validation failed
     */
    private function checkAuth(Request $request)
    {
        $validation = $this->validateSsoToken($request);
        if ($validation !== true) {
            return $validation;
        }
        return null;
    }

    // ==================== LEADS API ====================

    /**
     * Get all leads
     */
    public function getLeads(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $leads = Lead::where('created_by', Auth::user()->ownerId())
            ->with(['stage', 'pipeline'])
            ->get();

        // Add tags to each lead
        $leads->each(function ($lead) {
            $lead->tags_list = $lead->tags();
        });

        return response()->json([
            'success' => true,
            'data' => $leads,
            'message' => 'Leads retrieved successfully'
        ]);
    }

    /**
     * Get single lead
     */
    public function getLead(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $lead = Lead::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->with(['stage', 'pipeline', 'files', 'tasks', 'calls', 'emails', 'discussions'])
            ->first();

        if ($lead) {
            $lead->tags_list = $lead->tags();
        }

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $lead,
            'message' => 'Lead retrieved successfully'
        ]);
    }

    /**
     * Create new lead
     */
    public function createLead(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'subject' => 'required',
            'name' => 'required',
            'email' => 'required|email',
            'pipeline_id' => 'required|exists:pipelines,id',
            'stage_id' => 'required|exists:lead_stages,id',
            'sources' => 'required',
            'products' => 'nullable',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $lead = new Lead();
        $lead->subject = $request->subject;
        $lead->name = $request->name;
        $lead->email = $request->email;
        $lead->phone = $request->phone;
        $lead->pipeline_id = $request->pipeline_id;
        $lead->stage_id = $request->stage_id;
        $lead->sources = $request->sources;
        $lead->products = $request->products;
        $lead->notes = $request->notes;
        $lead->tags = $request->tags ? implode(',', $request->tags) : null;
        $lead->date = $request->date ?? now();
        $lead->next_follow_up_date = $request->next_follow_up_date;
        $lead->created_by = Auth::user()->ownerId();
        $lead->save();

        // Assign users to lead
        if ($request->has('users') && is_array($request->users)) {
            foreach ($request->users as $userId) {
                $userLead = new \App\Models\UserLead();
                $userLead->user_id = $userId;
                $userLead->lead_id = $lead->id;
                $userLead->save();
            }
        }

        // Send webhook
        try {
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchLeadCreated($lead);
        } catch (\Exception $e) {
            Log::error('Webhook dispatch failed for lead creation', ['error' => $e->getMessage()]);
        }

        return response()->json([
            'success' => true,
            'data' => $lead->load(['stage', 'pipeline']),
            'message' => 'Lead created successfully'
        ], 201);
    }

    /**
     * Update lead
     */
    public function updateLead(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $lead = Lead::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'subject' => 'required',
            'name' => 'required',
            'email' => 'required|email',
            'pipeline_id' => 'required|exists:pipelines,id',
            'stage_id' => 'required|exists:lead_stages,id',
            'sources' => 'required',
            'products' => 'nullable',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $oldStageId = $lead->stage_id;

        $lead->subject = $request->subject;
        $lead->name = $request->name;
        $lead->email = $request->email;
        $lead->phone = $request->phone;
        $lead->pipeline_id = $request->pipeline_id;
        $lead->stage_id = $request->stage_id;
        $lead->sources = $request->sources;
        $lead->products = $request->products;
        $lead->notes = $request->notes;
        $lead->labels = $request->labels ? implode(',', $request->labels) : null;
        $lead->date = $request->date ?? $lead->date;
        $lead->next_follow_up_date = $request->next_follow_up_date;
        $lead->save();

        // Send webhook for stage change if stage changed
        if ($oldStageId != $lead->stage_id) {
            try {
                $webhookDispatcher = new CrmWebhookDispatcher();
                $webhookDispatcher->dispatchLeadStageChanged($lead, $oldStageId, $lead->stage_id);
            } catch (\Exception $e) {
                Log::error('Webhook dispatch failed for lead stage change', ['error' => $e->getMessage()]);
            }
        }

        return response()->json([
            'success' => true,
            'data' => $lead->load(['stage', 'pipeline']),
            'message' => 'Lead updated successfully'
        ]);
    }

    /**
     * Delete lead
     */
    public function deleteLead(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $lead = Lead::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        // Delete related records
        \App\Models\LeadCall::where('lead_id', $lead->id)->delete();
        \App\Models\LeadEmail::where('lead_id', $lead->id)->delete();
        \App\Models\LeadDiscussion::where('lead_id', $lead->id)->delete();
        \App\Models\LeadFile::where('lead_id', $lead->id)->delete();
        \App\Models\UserLead::where('lead_id', $lead->id)->delete();
        \App\Models\LeadTask::where('lead_id', $lead->id)->delete();
        \App\Models\LeadActivityLog::where('lead_id', $lead->id)->delete();

        $lead->delete();

        return response()->json([
            'success' => true,
            'message' => 'Lead deleted successfully'
        ]);
    }

    // ==================== DEALS API ====================

    /**
     * Get all deals
     */
    public function getDeals(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $deals = Deal::where('created_by', Auth::user()->ownerId())
            ->with(['stage', 'pipeline'])
            ->get();

        // Add labels to each deal
        $deals->each(function ($deal) {
            $deal->labels_list = $deal->labelsList();
        });

        return response()->json([
            'success' => true,
            'data' => $deals,
            'message' => 'Deals retrieved successfully'
        ]);
    }

    /**
     * Get single deal
     */
    public function getDeal(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $deal = Deal::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->with(['stage', 'pipeline', 'files', 'tasks', 'calls', 'emails', 'discussions'])
            ->first();

        if ($deal) {
            $deal->labels_list = $deal->labelsList();
        }

        if (!$deal) {
            return response()->json(['error' => 'Deal not found'], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $deal,
            'message' => 'Deal retrieved successfully'
        ]);
    }

    /**
     * Create new deal
     */
    public function createDeal(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'pipeline_id' => 'required|exists:pipelines,id',
            'stage_id' => 'required|exists:stages,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $deal = new Deal();
        $deal->name = $request->name;
        $deal->phone = $request->phone;
        $deal->price = $request->price ?? 0;
        $deal->pipeline_id = $request->pipeline_id;
        $deal->stage_id = $request->stage_id;
        $deal->sources = $request->sources;
        $deal->products = $request->products;
        $deal->notes = $request->notes;
        $deal->labels = $request->labels ? implode(',', $request->labels) : null;
        $deal->status = $request->status ?? 'Active';
        $deal->created_by = Auth::user()->ownerId();
        $deal->save();

        // Assign clients to deal
        if ($request->has('clients') && is_array($request->clients)) {
            foreach ($request->clients as $clientId) {
                $clientDeal = new \App\Models\ClientDeal();
                $clientDeal->client_id = $clientId;
                $clientDeal->deal_id = $deal->id;
                $clientDeal->save();
            }
        }

        // Send webhook
        try {
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchDealCreated($deal);
        } catch (\Exception $e) {
            Log::error('Webhook dispatch failed for deal creation', ['error' => $e->getMessage()]);
        }

        return response()->json([
            'success' => true,
            'data' => $deal->load(['stage', 'pipeline', 'labels']),
            'message' => 'Deal created successfully'
        ], 201);
    }

    /**
     * Update deal
     */
    public function updateDeal(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $deal = Deal::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$deal) {
            return response()->json(['error' => 'Deal not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'pipeline_id' => 'required|exists:pipelines,id',
            'stage_id' => 'required|exists:stages,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $oldStageId = $deal->stage_id;

        $deal->name = $request->name;
        $deal->phone = $request->phone;
        $deal->price = $request->price ?? $deal->price;
        $deal->pipeline_id = $request->pipeline_id;
        $deal->stage_id = $request->stage_id;
        $deal->sources = $request->sources;
        $deal->products = $request->products;
        $deal->notes = $request->notes;
        $deal->labels = $request->labels ? implode(',', $request->labels) : null;
        $deal->status = $request->status ?? $deal->status;
        $deal->save();

        // Send webhook for stage change if stage changed
        if ($oldStageId != $deal->stage_id) {
            try {
                $webhookDispatcher = new CrmWebhookDispatcher();
                $webhookDispatcher->dispatchDealStageChanged($deal, $oldStageId, $deal->stage_id);
            } catch (\Exception $e) {
                Log::error('Webhook dispatch failed for deal stage change', ['error' => $e->getMessage()]);
            }
        }

        return response()->json([
            'success' => true,
            'data' => $deal->load(['stage', 'pipeline', 'labels']),
            'message' => 'Deal updated successfully'
        ]);
    }

    /**
     * Delete deal
     */
    public function deleteDeal(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $deal = Deal::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$deal) {
            return response()->json(['error' => 'Deal not found'], 404);
        }

        // Delete related records
        \App\Models\DealCall::where('deal_id', $deal->id)->delete();
        \App\Models\DealEmail::where('deal_id', $deal->id)->delete();
        \App\Models\DealDiscussion::where('deal_id', $deal->id)->delete();
        \App\Models\DealFile::where('deal_id', $deal->id)->delete();
        \App\Models\ClientDeal::where('deal_id', $deal->id)->delete();
        \App\Models\UserDeal::where('deal_id', $deal->id)->delete();
        \App\Models\DealTask::where('deal_id', $deal->id)->delete();
        \App\Models\ActivityLog::where('deal_id', $deal->id)->delete();

        $deal->delete();

        return response()->json([
            'success' => true,
            'message' => 'Deal deleted successfully'
        ]);
    }

    // ==================== CUSTOMERS API ====================

    /**
     * Get all customers
     */
    public function getCustomers(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $customers = Customer::where('created_by', Auth::user()->creatorId())->get();

        return response()->json([
            'success' => true,
            'data' => $customers,
            'message' => 'Customers retrieved successfully'
        ]);
    }

    /**
     * Get single customer
     */
    public function getCustomer(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $customer = Customer::where('created_by', Auth::user()->creatorId())
            ->where('id', $id)
            ->first();

        if (!$customer) {
            return response()->json(['error' => 'Customer not found'], 404);
        }

        return response()->json([
            'success' => true,
            'data' => $customer,
            'message' => 'Customer retrieved successfully'
        ]);
    }

    /**
     * Create new customer
     */
    public function createCustomer(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'contact' => 'required|regex:/^([0-9\s\-\+\(\)]*)$/',
            'email' => 'required|email|unique:customers,email',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $customer = new Customer();
        $customer->customer_id = $this->customerNumber();
        $customer->name = $request->name;
        $customer->contact = $request->contact;
        $customer->email = $request->email;
        $customer->tax_number = $request->tax_number;
        $customer->password = bcrypt($request->password ?? 'password');
        $customer->created_by = Auth::user()->creatorId();
        $customer->save();

        return response()->json([
            'success' => true,
            'data' => $customer,
            'message' => 'Customer created successfully'
        ], 201);
    }

    /**
     * Update customer
     */
    public function updateCustomer(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $customer = Customer::where('created_by', Auth::user()->creatorId())
            ->where('id', $id)
            ->first();

        if (!$customer) {
            return response()->json(['error' => 'Customer not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required',
            'contact' => 'required|regex:/^([0-9\s\-\+\(\)]*)$/',
            'email' => 'required|email|unique:customers,email,' . $customer->id,
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $customer->name = $request->name;
        $customer->contact = $request->contact;
        $customer->email = $request->email;
        $customer->tax_number = $request->tax_number;
        if ($request->password) {
            $customer->password = bcrypt($request->password);
        }
        $customer->save();

        return response()->json([
            'success' => true,
            'data' => $customer,
            'message' => 'Customer updated successfully'
        ]);
    }

    /**
     * Delete customer
     */
    public function deleteCustomer(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $customer = Customer::where('created_by', Auth::user()->creatorId())
            ->where('id', $id)
            ->first();

        if (!$customer) {
            return response()->json(['error' => 'Customer not found'], 404);
        }

        $customer->delete();

        return response()->json([
            'success' => true,
            'message' => 'Customer deleted successfully'
        ]);
    }

    /**
     * Generate customer number
     */
    private function customerNumber()
    {
        $latest = Customer::where('created_by', '=', Auth::user()->creatorId())->latest()->first();
        if (!$latest) {
            return 1;
        }
        return $latest->customer_id + 1;
    }

    // ==================== PIPELINES API ====================

    /**
     * Get all pipelines
     */
    public function getPipelines(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $pipelines = Pipeline::where('created_by', Auth::user()->creatorId())->get();

        return response()->json([
            'success' => true,
            'data' => $pipelines,
            'message' => 'Pipelines retrieved successfully'
        ]);
    }

    /**
     * Create new pipeline
     */
    public function createPipeline(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'name' => 'required|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $pipeline = new Pipeline();
        $pipeline->name = $request->name;
        $pipeline->created_by = Auth::user()->creatorId();
        $pipeline->save();

        return response()->json([
            'success' => true,
            'data' => $pipeline,
            'message' => 'Pipeline created successfully'
        ], 201);
    }

    /**
     * Update pipeline
     */
    public function updatePipeline(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $pipeline = Pipeline::where('created_by', Auth::user()->creatorId())
            ->where('id', $id)
            ->first();

        if (!$pipeline) {
            return response()->json(['error' => 'Pipeline not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'name' => 'required|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $pipeline->name = $request->name;
        $pipeline->save();

        return response()->json([
            'success' => true,
            'data' => $pipeline,
            'message' => 'Pipeline updated successfully'
        ]);
    }

    /**
     * Delete pipeline
     */
    public function deletePipeline(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $pipeline = Pipeline::where('created_by', Auth::user()->creatorId())
            ->where('id', $id)
            ->first();

        if (!$pipeline) {
            return response()->json(['error' => 'Pipeline not found'], 404);
        }

        $pipeline->delete();

        return response()->json([
            'success' => true,
            'message' => 'Pipeline deleted successfully'
        ]);
    }

    // ==================== STAGES API ====================

    /**
     * Get all stages
     */
    public function getStages(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $stages = Stage::select('stages.*', 'pipelines.name as pipeline')
            ->join('pipelines', 'pipelines.id', '=', 'stages.pipeline_id')
            ->where('pipelines.created_by', Auth::user()->ownerId())
            ->where('stages.created_by', Auth::user()->ownerId())
            ->orderBy('stages.pipeline_id')
            ->orderBy('stages.order')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $stages,
            'message' => 'Stages retrieved successfully'
        ]);
    }

    /**
     * Get stages by pipeline
     */
    public function getStagesByPipeline(Request $request, $pipelineId)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        // Check if the request specifies the type of stages to return
        $type = $request->get('type', 'lead'); // Default to lead stages
        
        if ($type === 'deal') {
            $stages = Stage::where('pipeline_id', $pipelineId)
                ->where('created_by', Auth::user()->ownerId())
                ->orderBy('order')
                ->get();
            $message = 'Deal stages retrieved successfully';
        } else {
            // Default to lead stages
            $stages = \App\Models\LeadStage::where('pipeline_id', $pipelineId)
                ->where('created_by', Auth::user()->ownerId())
                ->orderBy('order')
                ->get();
            $message = 'Lead stages retrieved successfully';
        }

        return response()->json([
            'success' => true,
            'data' => $stages,
            'message' => $message
        ]);
    }

    /**
     * Get lead stages by pipeline
     */
    public function getLeadStagesByPipeline(Request $request, $pipelineId)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $stages = \App\Models\LeadStage::where('pipeline_id', $pipelineId)
            ->where('created_by', Auth::user()->ownerId())
            ->orderBy('order')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $stages,
            'message' => 'Lead stages retrieved successfully'
        ]);
    }

    /**
     * Get deal stages by pipeline
     */
    public function getDealStagesByPipeline(Request $request, $pipelineId)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $stages = Stage::where('pipeline_id', $pipelineId)
            ->where('created_by', Auth::user()->ownerId())
            ->orderBy('order')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $stages,
            'message' => 'Deal stages retrieved successfully'
        ]);
    }

    /**
     * Create new stage
     */
    public function createStage(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'name' => 'required|max:20',
            'pipeline_id' => 'required|exists:pipelines,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $stage = new Stage();
        $stage->name = $request->name;
        $stage->pipeline_id = $request->pipeline_id;
        $stage->created_by = Auth::user()->ownerId();
        $stage->save();

        return response()->json([
            'success' => true,
            'data' => $stage,
            'message' => 'Stage created successfully'
        ], 201);
    }

    // ==================== SOURCES API ====================

    /**
     * Get all sources
     */
    public function getSources(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $sources = Source::where('created_by', Auth::user()->ownerId())->get();

        return response()->json([
            'success' => true,
            'data' => $sources,
            'message' => 'Sources retrieved successfully'
        ]);
    }

    /**
     * Create new source
     */
    public function createSource(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'name' => 'required|max:20',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $source = new Source();
        $source->name = $request->name;
        $source->created_by = Auth::user()->ownerId();
        $source->save();

        return response()->json([
            'success' => true,
            'data' => $source,
            'message' => 'Source created successfully'
        ], 201);
    }

    // ==================== LABELS API ====================

    /**
     * Get all labels
     */
    public function getLabels(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $labels = Label::select('labels.*', 'pipelines.name as pipeline')
            ->join('pipelines', 'pipelines.id', '=', 'labels.pipeline_id')
            ->where('labels.created_by', Auth::user()->ownerId())
            ->get();

        return response()->json([
            'success' => true,
            'data' => $labels,
            'message' => 'Labels retrieved successfully'
        ]);
    }

    /**
     * Create new label
     */
    public function createLabel(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'name' => 'required|max:20',
            'pipeline_id' => 'required|exists:pipelines,id',
            'color' => 'required',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $label = new Label();
        $label->name = $request->name;
        $label->pipeline_id = $request->pipeline_id;
        $label->color = $request->color;
        $label->created_by = Auth::user()->ownerId();
        $label->save();

        return response()->json([
            'success' => true,
            'data' => $label,
            'message' => 'Label created successfully'
        ], 201);
    }

    // ==================== TASKS API ====================

    /**
     * Get lead tasks
     */
    public function getLeadTasks(Request $request, $leadId)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $tasks = LeadTask::where('lead_id', $leadId)->get();

        return response()->json([
            'success' => true,
            'data' => $tasks,
            'message' => 'Lead tasks retrieved successfully'
        ]);
    }

    /**
     * Get deal tasks
     */
    public function getDealTasks(Request $request, $dealId)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $tasks = DealTask::where('deal_id', $dealId)->get();

        return response()->json([
            'success' => true,
            'data' => $tasks,
            'message' => 'Deal tasks retrieved successfully'
        ]);
    }

    // ==================== ACTIVITY LOGS API ====================

    /**
     * Get lead activity logs
     */
    public function getLeadActivityLogs(Request $request, $leadId)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $logs = LeadActivityLog::where('lead_id', $leadId)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $logs,
            'message' => 'Lead activity logs retrieved successfully'
        ]);
    }

    /**
     * Get deal activity logs
     */
    public function getDealActivityLogs(Request $request, $dealId)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $logs = ActivityLog::where('deal_id', $dealId)
            ->with('user')
            ->orderBy('created_at', 'desc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $logs,
            'message' => 'Deal activity logs retrieved successfully'
        ]);
    }

    // ==================== MOVE LEAD API ====================

    /**
     * Move lead to a different stage
     */
    public function moveLeadToStage(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'stage_id' => 'required|integer|exists:lead_stages,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $lead = Lead::where('created_by', Auth::user()->creatorId())
            ->where('id', $id)
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        $oldStage = $lead->stage;
        $newStage = LeadStage::find($request->stage_id);

        if (!$newStage) {
            return response()->json(['error' => 'Stage not found'], 404);
        }

        // Update the lead's stage
        $lead->stage_id = $request->stage_id;
        $lead->save();

        // Log the activity
        LeadActivityLog::create([
            'user_id' => Auth::user()->id,
            'lead_id' => $lead->id,
            'log_type' => 'Move',
            'remark' => json_encode([
                'title' => $lead->name,
                'old_status' => $oldStage->name,
                'new_status' => $newStage->name,
            ]),
        ]);

        // Send webhook for stage change
        try {
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchLeadStageChanged($lead, $oldStage->id, $request->stage_id);
        } catch (\Exception $e) {
            Log::error('Webhook dispatch failed for lead stage change', ['error' => $e->getMessage()]);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Lead moved from ' . $oldStage->name . ' to ' . $newStage->name,
            'lead_id' => $id,
            'old_stage_id' => $oldStage->id,
            'new_stage_id' => $lead->stage_id,
        ]);
    }

    /**
     * Move lead with order (Kanban style)
     */
    public function moveLeadWithOrder(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'lead_id' => 'required|integer|exists:leads,id',
            'stage_id' => 'required|integer|exists:lead_stages,id',
            'order' => 'array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $lead = Lead::where('created_by', Auth::user()->creatorId())
            ->where('id', $request->lead_id)
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        $oldStage = $lead->stage;
        $newStage = LeadStage::find($request->stage_id);

        if (!$newStage) {
            return response()->json(['error' => 'Stage not found'], 404);
        }

        // Update lead stage and order if provided
        if ($lead->stage_id != $request->stage_id) {
            // Log the activity only if stage actually changed
            LeadActivityLog::create([
                'user_id' => Auth::user()->id,
                'lead_id' => $lead->id,
                'log_type' => 'Move',
                'remark' => json_encode([
                    'title' => $lead->name,
                    'old_status' => $oldStage->name,
                    'new_status' => $newStage->name,
                ]),
            ]);

            // Send webhook for stage change
            try {
                $webhookDispatcher = new CrmWebhookDispatcher();
                $webhookDispatcher->dispatchLeadStageChanged($lead, $oldStage->id, $request->stage_id);
            } catch (\Exception $e) {
                Log::error('Webhook dispatch failed for lead stage change', ['error' => $e->getMessage()]);
            }
        }

        // Update order for all leads in the order array
        if ($request->has('order') && is_array($request->order)) {
            foreach ($request->order as $key => $leadId) {
                $orderLead = Lead::where('created_by', Auth::user()->creatorId())
                    ->where('id', $leadId)
                    ->first();
                if ($orderLead) {
                    $orderLead->order = $key;
                    $orderLead->stage_id = $request->stage_id;
                    $orderLead->save();
                }
            }
        } else {
            // Just update the single lead
            $lead->stage_id = $request->stage_id;
            $lead->save();
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Lead successfully moved.',
            'lead_id' => $lead->id,
            'old_stage_id' => $oldStage->id,
            'new_stage_id' => $request->stage_id,
        ]);
    }

    // ==================== MOVE DEAL API ====================

    /**
     * Move deal to a different stage
     */
    public function moveDealToStage(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'stage_id' => 'required|integer|exists:stages,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $deal = Deal::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$deal) {
            return response()->json(['error' => 'Deal not found'], 404);
        }

        $oldStage = $deal->stage;
        $newStage = Stage::find($request->stage_id);

        if (!$newStage) {
            return response()->json(['error' => 'Stage not found'], 404);
        }

        // Update the deal's stage
        $deal->stage_id = $request->stage_id;
        $deal->save();

        // Log the activity
        ActivityLog::create([
            'user_id' => Auth::user()->id,
            'deal_id' => $deal->id,
            'log_type' => 'Move',
            'remark' => json_encode([
                'title' => $deal->name,
                'old_status' => $oldStage->name,
                'new_status' => $newStage->name,
            ]),
        ]);

        // Send webhook for stage change
        try {
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchDealStageChanged($deal, $oldStage->id, $request->stage_id);
        } catch (\Exception $e) {
            Log::error('Webhook dispatch failed for deal stage change', ['error' => $e->getMessage()]);
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Deal moved from ' . $oldStage->name . ' to ' . $newStage->name,
            'deal_id' => $id,
            'old_stage_id' => $oldStage->id,
            'new_stage_id' => $deal->stage_id,
        ]);
    }

    /**
     * Move deal with order (Kanban style)
     */
    public function moveDealWithOrder(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'deal_id' => 'required|integer|exists:deals,id',
            'stage_id' => 'required|integer|exists:stages,id',
            'order' => 'array'
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $deal = Deal::where('created_by', Auth::user()->ownerId())
            ->where('id', $request->deal_id)
            ->first();

        if (!$deal) {
            return response()->json(['error' => 'Deal not found'], 404);
        }

        $oldStage = $deal->stage;
        $newStage = Stage::find($request->stage_id);

        if (!$newStage) {
            return response()->json(['error' => 'Stage not found'], 404);
        }

        // Update deal stage and order if provided
        if ($deal->stage_id != $request->stage_id) {
            // Log the activity only if stage actually changed
            ActivityLog::create([
                'user_id' => Auth::user()->id,
                'deal_id' => $deal->id,
                'log_type' => 'Move',
                'remark' => json_encode([
                    'title' => $deal->name,
                    'old_status' => $oldStage->name,
                    'new_status' => $newStage->name,
                ]),
            ]);

            // Send webhook for stage change
            try {
                $webhookDispatcher = new CrmWebhookDispatcher();
                $webhookDispatcher->dispatchDealStageChanged($deal, $oldStage->id, $request->stage_id);
            } catch (\Exception $e) {
                Log::error('Webhook dispatch failed for deal stage change', ['error' => $e->getMessage()]);
            }
        }

        // Update order for all deals in the order array
        if ($request->has('order') && is_array($request->order)) {
            foreach ($request->order as $key => $dealId) {
                $orderDeal = Deal::where('created_by', Auth::user()->ownerId())
                    ->where('id', $dealId)
                    ->first();
                if ($orderDeal) {
                    $orderDeal->order = $key;
                    $orderDeal->stage_id = $request->stage_id;
                    $orderDeal->save();
                }
            }
        } else {
            // Just update the single deal
            $deal->stage_id = $request->stage_id;
            $deal->save();
        }

        return response()->json([
            'status' => 'success',
            'message' => 'Deal successfully moved.'
        ]);
    }

    // ==================== LEAD ASSIGNMENT API ====================

    /**
     * Assign lead to users
     */
    public function assignLeadToUsers(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $lead = Lead::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        // Remove existing assignments
        \App\Models\UserLead::where('lead_id', $lead->id)->delete();

        // Add new assignments
        foreach ($request->user_ids as $userId) {
            $userLead = new \App\Models\UserLead();
            $userLead->user_id = $userId;
            $userLead->lead_id = $lead->id;
            $userLead->save();
        }

        // Send webhook
        try {
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchLeadUpdated($lead);
        } catch (\Exception $e) {
            \Log::error('Webhook dispatch failed for lead assignment', ['error' => $e->getMessage()]);
        }

        return response()->json([
            'success' => true,
            'message' => 'Lead assigned to users successfully',
            'lead_id' => $lead->id,
            'assigned_users' => $request->user_ids
        ]);
    }

    /**
     * Unassign lead from users
     */
    public function unassignLeadFromUsers(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $lead = Lead::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        // Remove specific assignments
        \App\Models\UserLead::where('lead_id', $lead->id)
            ->whereIn('user_id', $request->user_ids)
            ->delete();

        return response()->json([
            'success' => true,
            'message' => 'Lead unassigned from users successfully',
            'lead_id' => $lead->id,
            'unassigned_users' => $request->user_ids
        ]);
    }

    // ==================== LEAD PIPELINE API ====================

    /**
     * Move lead to different pipeline
     */
    public function moveLeadToPipeline(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'pipeline_id' => 'required|exists:pipelines,id',
            'stage_id' => 'required|exists:lead_stages,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $lead = Lead::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        $oldPipeline = $lead->pipeline;
        $newPipeline = Pipeline::find($request->pipeline_id);
        $newStage = LeadStage::find($request->stage_id);

        // Verify stage belongs to the pipeline
        if ($newStage->pipeline_id != $request->pipeline_id) {
            return response()->json([
                'error' => 'Stage does not belong to the specified pipeline'
            ], 422);
        }

        $lead->pipeline_id = $request->pipeline_id;
        $lead->stage_id = $request->stage_id;
        $lead->save();

        // Send webhook
        try {
            $webhookDispatcher = new CrmWebhookDispatcher();
            $webhookDispatcher->dispatchLeadUpdated($lead);
        } catch (\Exception $e) {
            \Log::error('Webhook dispatch failed for lead pipeline move', ['error' => $e->getMessage()]);
        }

        return response()->json([
            'status' => 'success',
            'message' => "Lead moved from {$oldPipeline->name} to {$newPipeline->name}",
            'lead_id' => $lead->id,
            'old_pipeline_id' => $oldPipeline->id,
            'new_pipeline_id' => $request->pipeline_id,
            'new_stage_id' => $request->stage_id
        ]);
    }

    // ==================== LEAD LABELS/TAGS API ====================

    /**
     * Add labels to lead
     */
    public function addLeadTags(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'tag_ids' => 'required|array',
            'tag_ids.*' => 'exists:tags,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $lead = Lead::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        // Get existing tags
        $existingTags = $lead->tags ? explode(',', $lead->tags) : [];

        // Add new tags (avoid duplicates)
        $newTags = array_unique(array_merge($existingTags, $request->tag_ids));

        $lead->tags = implode(',', $newTags);
        $lead->save();

        return response()->json([
            'success' => true,
            'message' => 'Tags added to lead successfully',
            'lead_id' => $lead->id,
            'tags' => $newTags
        ]);
    }

    /**
     * Remove tags from lead
     */
    public function removeLeadTags(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'tag_ids' => 'required|array',
            'tag_ids.*' => 'exists:tags,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $lead = Lead::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        // Get existing tags
        $existingTags = $lead->tags ? explode(',', $lead->tags) : [];

        // Remove specified tags
        $remainingTags = array_diff($existingTags, $request->tag_ids);

        $lead->tags = !empty($remainingTags) ? implode(',', $remainingTags) : null;
        $lead->save();

        return response()->json([
            'success' => true,
            'message' => 'Tags removed from lead successfully',
            'lead_id' => $lead->id,
            'remaining_tags' => array_values($remainingTags)
        ]);
    }

    // ==================== LEAD EXPORT API ====================

    /**
     * Export all leads
     */
    public function exportLeads(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $leads = Lead::where('created_by', Auth::user()->ownerId())
            ->with(['stage', 'pipeline'])
            ->get();

        $exportData = $leads->map(function ($lead) {
            return [
                'id' => $lead->id,
                'subject' => $lead->subject,
                'name' => $lead->name,
                'email' => $lead->email,
                'pipeline' => $lead->pipeline->name ?? '',
                'stage' => $lead->stage->name ?? '',
                'sources' => $lead->sources,
                'products' => $lead->products,
                'notes' => $lead->notes,
                'tags' => $lead->tags(),
                'date' => $lead->date,
                'next_follow_up_date' => $lead->next_follow_up_date,
                'created_at' => $lead->created_at,
                'updated_at' => $lead->updated_at,
            ];
        });

        return response()->json([
            'success' => true,
            'data' => $exportData,
            'total_records' => $exportData->count(),
            'message' => 'Leads exported successfully'
        ]);
    }

    /**
     * Export single lead
     */
    public function exportSingleLead(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $lead = Lead::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->with(['stage', 'pipeline', 'files', 'tasks', 'calls', 'emails', 'discussions'])
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        $exportData = [
            'id' => $lead->id,
            'subject' => $lead->subject,
            'name' => $lead->name,
            'email' => $lead->email,
            'pipeline' => $lead->pipeline->name ?? '',
            'stage' => $lead->stage->name ?? '',
            'sources' => $lead->sources,
            'products' => $lead->products,
            'notes' => $lead->notes,
            'tags' => $lead->tags(),
            'date' => $lead->date,
            'next_follow_up_date' => $lead->next_follow_up_date,
            'files' => $lead->files,
            'tasks' => $lead->tasks,
            'calls' => $lead->calls,
            'emails' => $lead->emails,
            'discussions' => $lead->discussions,
            'created_at' => $lead->created_at,
            'updated_at' => $lead->updated_at,
        ];

        return response()->json([
            'success' => true,
            'data' => $exportData,
            'message' => 'Lead exported successfully'
        ]);
    }

    // ==================== LEAD CUSTOM FIELDS API ====================

    /**
     * Get lead custom fields
     */
    public function getLeadCustomFields(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $lead = Lead::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        // Get custom fields for leads module (only active ones)
        $customFields = CustomField::where('module', 'lead')
            ->where('created_by', Auth::user()->creatorId())
            ->where('status', true)
            ->get();

        // Get custom field values for this lead
        $customFieldData = [];
        foreach ($customFields as $field) {
            $value = CustomFieldValue::where('field_id', $field->id)
                ->where('record_id', $lead->id)
                ->value('value');

            $customFieldData[] = [
                'field_id' => $field->id,
                'field_name' => $field->name,
                'field_type' => $field->type,
                'value' => $value
            ];
        }

        return response()->json([
            'success' => true,
            'data' => $customFieldData,
            'message' => 'Lead custom fields retrieved successfully'
        ]);
    }

    /**
     * Update lead custom fields
     */
    public function updateLeadCustomFields(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $lead = Lead::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        $validator = Validator::make($request->all(), [
            'custom_fields' => 'required|array',
            'custom_fields.*.field_id' => 'required|exists:custom_fields,id',
            'custom_fields.*.value' => 'nullable',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        // Update custom field values
        foreach ($request->custom_fields as $fieldData) {
            CustomFieldValue::updateOrCreate(
                [
                    'field_id' => $fieldData['field_id'],
                    'record_id' => $lead->id
                ],
                [
                    'value' => $fieldData['value']
                ]
            );
        }

        return response()->json([
            'success' => true,
            'message' => 'Lead custom fields updated successfully',
            'lead_id' => $lead->id
        ]);
    }

    // ==================== USERS API ====================

    /**
     * Get all users for assignment dropdowns
     */
    public function getUsers(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $users = User::where('created_by', Auth::user()->creatorId())
            ->where('type', '!=', 'client')
            ->where('is_active', 1)
            ->select('id', 'name', 'email', 'type')
            ->orderBy('name', 'asc')
            ->get();

        return response()->json([
            'success' => true,
            'data' => $users,
            'message' => 'Users retrieved successfully'
        ]);
    }

    // ==================== DEAL ASSIGNMENT API ====================

    /**
     * Assign deal to users
     */
    public function assignDealToUsers(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $deal = Deal::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$deal) {
            return response()->json(['error' => 'Deal not found'], 404);
        }

        // Remove existing assignments
        \App\Models\UserDeal::where('deal_id', $deal->id)->delete();

        // Add new assignments
        foreach ($request->user_ids as $userId) {
            $userDeal = new \App\Models\UserDeal();
            $userDeal->user_id = $userId;
            $userDeal->deal_id = $deal->id;
            $userDeal->save();
        }

        // Send webhook (optional, if you have one for deal assignment)
        // try {
        //     $webhookDispatcher = new CrmWebhookDispatcher();
        //     $webhookDispatcher->dispatchDealAssigned($deal, $request->user_ids);
        // } catch (\Exception $e) {
        //     \Log::error('Webhook dispatch failed for deal assignment', ['error' => $e->getMessage()]);
        // }

        return response()->json([
            'success' => true,
            'message' => 'Deal assigned to users successfully',
            'deal_id' => $deal->id,
            'assigned_users' => $request->user_ids
        ]);
    }

    /**
     * Unassign deal from users
     */
    public function unassignDealFromUsers(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'user_ids' => 'required|array',
            'user_ids.*' => 'exists:users,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $deal = Deal::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$deal) {
            return response()->json(['error' => 'Deal not found'], 404);
        }

        // Remove specific assignments
        \App\Models\UserDeal::where('deal_id', $deal->id)
            ->whereIn('user_id', $request->user_ids)
            ->delete();

        return response()->json([
            'success' => true,
            'message' => 'Deal unassigned from users successfully',
            'deal_id' => $deal->id,
            'unassigned_users' => $request->user_ids
        ]);
    }

    // ==================== DEAL PIPELINE API ====================

    /**
     * Move deal to different pipeline
     */
    public function moveDealToPipeline(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'pipeline_id' => 'required|exists:pipelines,id',
            'stage_id' => 'required|exists:stages,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $deal = Deal::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$deal) {
            return response()->json(['error' => 'Deal not found'], 404);
        }

        $oldPipeline = $deal->pipeline;
        $newPipeline = \App\Models\Pipeline::find($request->pipeline_id);
        $newStage = \App\Models\Stage::find($request->stage_id);

        // Verify stage belongs to the pipeline
        if ($newStage->pipeline_id != $request->pipeline_id) {
            return response()->json([
                'error' => 'Stage does not belong to the specified pipeline'
            ], 422);
        }

        $deal->pipeline_id = $request->pipeline_id;
        $deal->stage_id = $request->stage_id;
        $deal->save();

        // Send webhook (optional, if you have one for deal pipeline move)
        // try {
        //     $webhookDispatcher = new CrmWebhookDispatcher();
        //     $webhookDispatcher->dispatchDealUpdated($deal);
        // } catch (\Exception $e) {
        //     \Log::error('Webhook dispatch failed for deal pipeline move', ['error' => $e->getMessage()]);
        // }

        return response()->json([
            'status' => 'success',
            'message' => "Deal moved from {$oldPipeline->name} to {$newPipeline->name}",
            'deal_id' => $deal->id,
            'old_pipeline_id' => $oldPipeline->id,
            'new_pipeline_id' => $request->pipeline_id,
            'new_stage_id' => $request->stage_id
        ]);
    }

    // ==================== DEAL LABELS/TAGS API ====================

    /**
     * Add labels to deal
     */
    public function addDealLabels(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'label_ids' => 'required|array',
            'label_ids.*' => 'exists:labels,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $deal = Deal::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$deal) {
            return response()->json(['error' => 'Deal not found'], 404);
        }

        // Get existing labels
        $existingLabels = $deal->labels ? explode(',', $deal->labels) : [];

        // Add new labels (avoid duplicates)
        $newLabels = array_unique(array_merge($existingLabels, $request->label_ids));

        $deal->labels = implode(',', $newLabels);
        $deal->save();

        return response()->json([
            'success' => true,
            'message' => 'Labels added to deal successfully',
            'deal_id' => $deal->id,
            'labels' => $newLabels
        ]);
    }

    /**
     * Remove labels from deal
     */
    public function removeDealLabels(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $validator = Validator::make($request->all(), [
            'label_ids' => 'required|array',
            'label_ids.*' => 'exists:labels,id',
        ]);

        if ($validator->fails()) {
            return response()->json([
                'error' => 'Validation failed',
                'messages' => $validator->errors()
            ], 422);
        }

        $deal = Deal::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->first();

        if (!$deal) {
            return response()->json(['error' => 'Deal not found'], 404);
        }

        // Get existing labels
        $existingLabels = $deal->labels ? explode(',', $deal->labels) : [];

        // Remove specified labels
        $remainingLabels = array_diff($existingLabels, $request->label_ids);

        $deal->labels = implode(',', $remainingLabels);
        $deal->save();

        return response()->json([
            'success' => true,
            'message' => 'Labels removed from deal successfully',
            'deal_id' => $deal->id,
            'labels' => $remainingLabels
        ]);
    }

    // ==================== CREATE DEAL FROM LEAD API ====================

    /**
     * Create deal from lead
     */
    public function createDealFromLead(Request $request, $leadId)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $lead = \App\Models\Lead::where('created_by', Auth::user()->ownerId())
            ->where('id', $leadId)
            ->first();

        if (!$lead) {
            return response()->json(['error' => 'Lead not found'], 404);
        }

        $stage = \App\Models\Stage::where('pipeline_id', $lead->pipeline_id)->first();
        if (empty($stage)) {
            return response()->json(['error' => 'Please create stage for this pipeline.'], 422);
        }

        $deal = new Deal();
        $deal->name = $lead->name;
        $deal->price = $request->price ?? 0;
        $deal->pipeline_id = $lead->pipeline_id;
        $deal->stage_id = $stage->id;
        $deal->sources = $lead->sources;
        $deal->products = $lead->products;
        $deal->notes = $lead->notes;
        $deal->tags = $lead->tags;
        $deal->status = 'Active';
        $deal->created_by = $lead->created_by;
        $deal->save();

        // Optionally assign clients and users from lead
        if ($request->has('clients') && is_array($request->clients)) {
            foreach ($request->clients as $clientId) {
                $clientDeal = new \App\Models\ClientDeal();
                $clientDeal->client_id = $clientId;
                $clientDeal->deal_id = $deal->id;
                $clientDeal->save();
            }
        }
        $leadUsers = \App\Models\UserLead::where('lead_id', $lead->id)->get();
        foreach ($leadUsers as $leadUser) {
            $userDeal = new \App\Models\UserDeal();
            $userDeal->user_id = $leadUser->user_id;
            $userDeal->deal_id = $deal->id;
            $userDeal->save();
        }

        // Send webhook (optional)
        // try {
        //     $webhookDispatcher = new CrmWebhookDispatcher();
        //     $webhookDispatcher->dispatchDealCreated($deal);
        // } catch (\Exception $e) {
        //     \Log::error('Webhook dispatch failed for deal creation from lead', ['error' => $e->getMessage()]);
        // }

        return response()->json([
            'success' => true,
            'data' => $deal->load(['stage', 'pipeline', 'tags']),
            'message' => 'Deal created from lead successfully'
        ], 201);
    }

    // ==================== CONTACTS API ====================

    /**
     * Get all contacts (leads and deals combined)
     */
    public function getContacts(Request $request)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        $contacts = [];

        // Build leads query
        $leadsQuery = Lead::where('created_by', Auth::user()->ownerId());

        // Apply filters for leads
        if ($request->filled('filter_name')) {
            $leadsQuery->where('name', 'like', '%' . $request->filter_name . '%');
        }
        if ($request->filled('filter_email')) {
            $leadsQuery->where('email', 'like', '%' . $request->filter_email . '%');
        }
        if ($request->filled('filter_phone')) {
            $leadsQuery->where('phone', 'like', '%' . $request->filter_phone . '%');
        }

        // Fetch leads data with contact group information
        $leads = $leadsQuery->with('contactGroup')->get();
        foreach ($leads as $lead) {
            $contacts[] = [
                'id' => $lead->id,
                'name' => $lead->name,
                'email' => $lead->email ?? '',
                'phone' => $lead->phone ?? '',
                'type' => 'Lead',
                'contact_group' => $lead->contactGroup ? $lead->contactGroup->name : null,
                'pipeline' => $lead->pipeline ? $lead->pipeline->name : null,
                'stage' => $lead->stage ? $lead->stage->name : null,
                'source' => $lead->sources,
                'notes' => $lead->notes,
                'tags' => $lead->tags,
                'status' => $lead->status,
                'created_at' => $lead->created_at,
                'updated_at' => $lead->updated_at
            ];
        }

        // Build deals query
        $dealsQuery = Deal::where('created_by', Auth::user()->ownerId());

        // Apply filters for deals
        if ($request->filled('filter_name')) {
            $dealsQuery->where('name', 'like', '%' . $request->filter_name . '%');
        }
        if ($request->filled('filter_phone')) {
            $dealsQuery->where('phone', 'like', '%' . $request->filter_phone . '%');
        }

        // Fetch deals data
        $deals = $dealsQuery->with(['pipeline', 'stage'])->get();
        foreach ($deals as $deal) {
            $contacts[] = [
                'id' => $deal->id,
                'name' => $deal->name ?? 'No Name',
                'email' => '', // Deals don't have email field
                'phone' => $deal->phone ?? '',
                'type' => 'Deal',
                'contact_group' => null, // Deals don't have contact groups
                'pipeline' => $deal->pipeline ? $deal->pipeline->name : null,
                'stage' => $deal->stage ? $deal->stage->name : null,
                'source' => $deal->sources,
                'notes' => $deal->notes,
                'tags' => $deal->tags,
                'status' => $deal->status,
                'price' => $deal->price,
                'created_at' => $deal->created_at,
                'updated_at' => $deal->updated_at
            ];
        }

        // Apply type filter (only for internal contacts - leads and deals)
        if ($request->filled('filter_type') && $request->filter_type !== 'WhatsApp') {
            $contacts = array_filter($contacts, function($contact) use ($request) {
                return $contact['type'] === $request->filter_type;
            });
        }

        // Sort contacts by created_at (newest first)
        usort($contacts, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });

        return response()->json([
            'success' => true,
            'data' => $contacts,
            'total' => count($contacts)
        ]);
    }

    /**
     * Get a specific contact by ID
     */
    public function getContact(Request $request, $id)
    {
        if ($authError = $this->checkAuth($request)) return $authError;

        // Try to find as lead first
        $lead = Lead::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->with(['contactGroup', 'pipeline', 'stage'])
            ->first();

        if ($lead) {
            $contact = [
                'id' => $lead->id,
                'name' => $lead->name,
                'email' => $lead->email ?? '',
                'phone' => $lead->phone ?? '',
                'type' => 'Lead',
                'contact_group' => $lead->contactGroup ? $lead->contactGroup->name : null,
                'pipeline' => $lead->pipeline ? $lead->pipeline->name : null,
                'stage' => $lead->stage ? $lead->stage->name : null,
                'source' => $lead->sources,
                'notes' => $lead->notes,
                'tags' => $lead->tags,
                'status' => $lead->status,
                'created_at' => $lead->created_at,
                'updated_at' => $lead->updated_at
            ];

            return response()->json([
                'success' => true,
                'data' => $contact
            ]);
        }

        // Try to find as deal
        $deal = Deal::where('created_by', Auth::user()->ownerId())
            ->where('id', $id)
            ->with(['pipeline', 'stage'])
            ->first();

        if ($deal) {
            $contact = [
                'id' => $deal->id,
                'name' => $deal->name ?? 'No Name',
                'email' => '', // Deals don't have email field
                'phone' => $deal->phone ?? '',
                'type' => 'Deal',
                'contact_group' => null, // Deals don't have contact groups
                'pipeline' => $deal->pipeline ? $deal->pipeline->name : null,
                'stage' => $deal->stage ? $deal->stage->name : null,
                'source' => $deal->sources,
                'notes' => $deal->notes,
                'tags' => $deal->tags,
                'status' => $deal->status,
                'price' => $deal->price,
                'created_at' => $deal->created_at,
                'updated_at' => $deal->updated_at
            ];

            return response()->json([
                'success' => true,
                'data' => $contact
            ]);
        }

        return response()->json([
            'error' => 'Contact not found'
        ], 404);
    }
}
